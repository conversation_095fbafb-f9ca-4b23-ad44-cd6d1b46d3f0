# SynapseAI Project Structure

## Monorepo Organization

SynapseAI follows a monorepo structure with the following main packages:

```
synapseai/
├── packages/
│   ├── backend/         # NestJS backend application
│   ├── frontend/        # Next.js frontend application
│   ├── shared/          # Shared types, utilities, and constants
│   ├── sdk/             # Client SDK for integration
│   └── cli/             # Command-line tools
├── tools/               # Build and development tools
├── docs/                # Documentation
└── config/              # Shared configuration files
```

## Backend Structure (NestJS)

```
backend/
├── src/
│   ├── main.ts                  # Application entry point
│   ├── app.module.ts            # Root module
│   ├── common/                  # Shared utilities, guards, filters
│   ├── config/                  # Configuration
│   ├── modules/                 # Feature modules
│   │   ├── auth/                # Authentication module
│   │   ├── agent/               # Agent module
│   │   ├── tool/                # Tool module
│   │   ├── tool-agent/          # Tool Agent module
│   │   ├── provider/            # Provider module
│   │   ├── session/             # Session module
│   │   ├── hitl/                # Human-in-the-Loop module
│   │   ├── knowledge-base/      # Knowledge Base module
│   │   ├── widget/              # Widget module
│   │   ├── analytics/           # Analytics module
│   │   └── admin/               # Admin module
│   ├── apix/                    # WebSocket gateway and events
│   └── uaui/                    # Universal AI User Interface engine
├── test/                        # Tests
└── dist/                        # Build output
```

### Module Structure

Each feature module follows a consistent structure:

```
module/
├── controllers/        # HTTP controllers
├── services/           # Business logic
├── entities/           # Database entities
├── repositories/       # Data access
├── dto/                # Data transfer objects
├── interfaces/         # Type definitions
├── events/             # Event definitions
├── guards/             # Authorization guards
├── pipes/              # Validation pipes
└── module.ts           # Module definition
```

## Frontend Structure (Next.js)

```
frontend/
├── app/                # App Router pages
│   ├── (auth)/         # Authentication routes
│   ├── dashboard/      # Dashboard routes
│   ├── agents/         # Agent management
│   ├── tools/          # Tool management
│   ├── tool-agents/    # Tool Agent management
│   ├── providers/      # Provider management
│   ├── knowledge-base/ # Knowledge Base management
│   ├── widgets/        # Widget management
│   ├── analytics/      # Analytics dashboard
│   ├── admin/          # Admin panel
│   ├── sandbox/        # Live Preview Sandbox
│   └── api/            # API routes
├── components/         # React components
│   ├── ui/             # UI components
│   ├── forms/          # Form components
│   ├── layouts/        # Layout components
│   └── features/       # Feature-specific components
├── lib/                # Utilities and helpers
├── hooks/              # Custom React hooks
├── store/              # Zustand store
├── styles/             # Global styles
├── public/             # Static assets
└── types/              # TypeScript type definitions
```

## Shared Package Structure

```
shared/
├── src/
│   ├── types/          # Shared type definitions
│   │   ├── api/        # API types
│   │   ├── apix/       # WebSocket event types
│   │   ├── models/     # Data model types
│   │   └── uaui/       # UAUI engine types
│   ├── constants/      # Shared constants
│   ├── utils/          # Shared utilities
│   └── schemas/        # Zod schemas
└── dist/               # Build output
```

## SDK Package Structure

```
sdk/
├── src/
│   ├── client/         # WebSocket client
│   ├── types/          # Type definitions
│   ├── utils/          # Utilities
│   └── index.ts        # Entry point
└── dist/               # Build output
```

## CLI Package Structure

```
cli/
├── src/
│   ├── commands/       # CLI commands
│   ├── templates/      # Project templates
│   ├── utils/          # Utilities
│   └── index.ts        # Entry point
└── dist/               # Build output
```

## Data Flow Architecture

SynapseAI follows a clear data flow architecture:

1. **HTTP Requests**: REST API for CRUD operations
2. **WebSocket Events**: Real-time communication using APIX protocol
3. **Database Access**: Repository pattern for data access
4. **Caching**: Redis for caching and session management
5. **Event Propagation**: Event-driven architecture for system events

## Naming Conventions

- **Directories**: kebab-case
- **Files**: kebab-case with appropriate extensions
- **Components**: PascalCase
- **Modules**: kebab-case for directories, PascalCase for class names

## Import Conventions

- Use absolute imports for cross-module references
- Use relative imports for intra-module references
- Group imports by source (built-in, external, internal)
# SynapseAI Technical Stack

## Core Technologies

### Backend
- **Framework**: NestJS (Node.js)
- **Language**: TypeScript
- **Database**: PostgreSQL with TypeORM
- **Caching/Session**: Redis
- **Real-time Communication**: WebSockets (custom APIX protocol)

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS with Shadcn UI
- **State Management**: Zustand
- **Data Fetching**: React Query

## Development Environment

### Prerequisites
- Node.js (v18+)
- PostgreSQL (v14+)
- Redis (v6+)

### Package Management
- pnpm (workspace monorepo)

## Project Structure
- Monorepo with workspace packages for backend, frontend, and shared libraries

## Common Commands

### Development
```bash
# Install dependencies
pnpm install

# Start development server (backend)
pnpm dev:backend

# Start development server (frontend)
pnpm dev:frontend

# Start all services
pnpm dev
```

### Testing
```bash
# Run unit tests
pnpm test

# Run integration tests
pnpm test:integration

# Run e2e tests
pnpm test:e2e

# Run all tests with coverage
pnpm test:coverage
```

### Building
```bash
# Build all packages
pnpm build

# Build specific package
pnpm build --filter=@synapseai/backend
pnpm build --filter=@synapseai/frontend
```

### Deployment
```bash
# Deploy with PM2
pnpm deploy:pm2

# Generate SSL certificates with Certbot
pnpm deploy:ssl
```

## Code Style and Conventions

### TypeScript
- Strict mode enabled
- Use interfaces for data models
- Use enums for fixed sets of values
- Use type guards for runtime type checking

### API Design
- RESTful API for CRUD operations
- WebSocket for real-time communication
- Use DTOs for request/response validation
- Follow OpenAPI specification

### Testing
- Jest for unit and integration tests
- Cypress for e2e tests
- Test coverage target: 80%+

### Naming Conventions
- **Files**: kebab-case (e.g., `user-service.ts`)
- **Classes**: PascalCase (e.g., `UserService`)
- **Interfaces**: PascalCase with "I" prefix (e.g., `IUserData`)
- **Variables/Functions**: camelCase (e.g., `getUserById`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_RETRY_COUNT`)

## Documentation
- JSDoc for code documentation
- OpenAPI for API documentation
- Markdown for project documentation
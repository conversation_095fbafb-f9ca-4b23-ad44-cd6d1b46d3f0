# Design Document

## Overview

SynapseAI is a universal, event-based, click-configurable AI orchestration system built on a modern tech stack including NestJS, Next.js, PostgreSQL, and Redis. The system is designed to be modular, scalable, and production-ready, with a focus on real-time communication, state management, and extensibility.

This design document outlines the architecture, components, data models, and technical decisions for implementing SynapseAI according to the requirements.

## Architecture

### System Architecture

SynapseAI follows a microservices-inspired architecture with clear separation of concerns, while maintaining a monorepo structure for ease of development and deployment. The system consists of the following main components:

1. **Backend (NestJS)**
   - REST API for CRUD operations
   - WebSocket Gateway for real-time communication (APIX)
   - Module-based architecture for core features
   - Integration with PostgreSQL and Redis
   - Authentication and authorization middleware

2. **Frontend (Next.js 14 App Router)**
   - Modern React application with App Router
   - Tailwind CSS and Shadcn UI for styling
   - Zustand for state management
   - WebSocket client for real-time communication
   - Responsive design for various device types

3. **Database Layer**
   - PostgreSQL for persistent storage
   - Redis for session management, caching, and pub/sub

4. **Infrastructure**
   - PM2 for process management
   - NGINX for reverse proxy and load balancing
   - Cert<PERSON> for SSL certificate management

### High-Level Architecture Diagram

```mermaid
graph TD
    Client[Client Browser] --> FE[Frontend: Next.js]
    Client --> WS[WebSocket Connection]
    FE --> API[Backend API: NestJS]
    WS --> APIX[APIX WebSocket Gateway]
    API --> DB[(PostgreSQL)]
    API --> Cache[(Redis)]
    APIX --> Cache
    APIX --> LLM[LLM Providers]
    API --> LLM
    
    subgraph "Backend Modules"
        AuthModule[Auth Module]
        AgentModule[Agent Module]
        ToolModule[Tool Module]
        ProviderModule[Provider Module]
        SessionModule[Session Module]
        HITLModule[HITL Module]
        KBModule[Knowledge Base Module]
        AnalyticsModule[Analytics Module]
        AdminModule[Admin Module]
    end
    
    API --> Backend Modules
    APIX --> Backend Modules
```

### Communication Flow

```mermaid
sequenceDiagram
    participant Client
    participant Frontend
    participant APIX
    participant Backend
    participant Redis
    participant LLM
    participant Tool
    
    Client->>Frontend: User Input
    Frontend->>APIX: user_message event
    APIX->>Redis: Get/Update Session
    APIX->>Frontend: thinking_status event
    APIX->>Backend: Process Request
    Backend->>LLM: API Request
    LLM-->>Backend: Streaming Response
    Backend-->>APIX: Process Chunks
    APIX-->>Frontend: text_chunk events
    
    alt Tool Execution
        Backend->>APIX: Tool Call Detected
        APIX->>Frontend: tool_call_start event
        APIX->>Tool: Execute Tool
        Tool-->>APIX: Tool Result
        APIX->>Frontend: tool_call_result event
        APIX->>Backend: Continue Processing
    end
    
    alt Human-in-the-Loop
        Backend->>APIX: User Input Required
        APIX->>Frontend: request_user_input event
        Frontend->>Client: Display Input Request
        Client->>Frontend: Provide Input
        Frontend->>APIX: user_response event
        APIX->>Backend: Continue Processing
    end
    
    Backend->>Redis: Update Session State
    APIX->>Frontend: state_update event
```

## Components and Interfaces

### Core Components

#### 1. UAUI Engine

The UAUI (Universal AI User Interface) Engine is the core reasoning and orchestration component of SynapseAI. It handles agent reasoning, memory access, provider selection, and tool integration.

**Key Components:**
- **UAUICore**: Main entry point for processing AI requests
- **SmartProviderSelector**: Routes requests to appropriate LLM providers
- **EventBus**: Manages system-wide events
- **StateManager**: Handles state propagation and synchronization
- **RouterEngine**: Manages cross-app commands

#### 2. APIX WebSocket Protocol

APIX is the WebSocket-based communication protocol that enables real-time interaction between the frontend and backend. It defines a set of standardized events for various system operations.

**Key Events:**
- `user_message`: User input to the system
- `thinking_status`: Indicates the system is processing
- `text_chunk`: Streaming response chunks
- `tool_call_start`, `tool_call_result`, `tool_call_error`: Tool execution events
- `request_user_input`, `user_response`: HITL interaction events
- `state_update`: Session state changes
- `error`: Error notifications
- `control_signal`: System control commands

#### 3. Backend Modules

Each core feature is implemented as a NestJS module with clear boundaries and responsibilities:

- **AuthModule**: Handles authentication, authorization, and user management
- **AgentModule**: Manages AI agents and their configurations
- **ToolModule**: Handles tool definitions, execution, and management
- **ToolAgentModule**: Combines agents and tools into hybrid workflows
- **ProviderModule**: Manages LLM provider configurations and routing
- **SessionModule**: Handles session state and persistence
- **HITLModule**: Manages human-in-the-loop interactions
- **KnowledgeBaseModule**: Handles document storage, vectorization, and retrieval
- **WidgetModule**: Manages widget configurations and generation
- **AnalyticsModule**: Tracks and reports system metrics
- **AdminModule**: Provides administrative interfaces

#### 4. Frontend Components

The frontend is organized into feature-based components with a focus on reusability and composition:

- **Layout Components**: Shell, navigation, and common UI elements
- **Feature Components**: UI for each core module
- **Form Components**: Reusable form elements and validation
- **Visualization Components**: Charts, graphs, and data displays
- **Widget Components**: Embeddable UI components

### Interfaces

#### 1. APIX Request/Response Interface

```typescript
export interface AIPXRequest {
  userId: string;
  sessionId: string;
  message: string;
  appType: 'widget' | 'dashboard' | 'crm';
  metadata?: Record<string, any>;
}

export interface AIPXResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
}
```

#### 2. UAUI Core Interface

```typescript
interface UAUICore {
  processRequest(request: AIPXRequest): Promise<AIPXResponse>;
  getSession(sessionId: string): Promise<SessionData>;
  updateSession(sessionId: string, data: Partial<SessionData>): Promise<void>;
  selectProvider(request: AIPXRequest): Promise<AIProvider>;
  executeAgent(agentId: string, input: string, context: AgentContext): Promise<AgentResponse>;
  executeTool(toolId: string, params: Record<string, any>): Promise<ToolResponse>;
}
```

#### 3. Provider Interface

```typescript
interface AIProvider {
  id: string;
  name: string;
  type: 'openai' | 'anthropic' | 'google' | 'mistral' | 'groq';
  models: string[];
  apiKey: string;
  baseUrl?: string;
  organization?: string;
  
  generateText(prompt: string, options: GenerateOptions): Promise<string>;
  generateStream(prompt: string, options: GenerateOptions): AsyncGenerator<string>;
  embedText(text: string): Promise<number[]>;
}
```

#### 4. Tool Interface

```typescript
interface Tool {
  id: string;
  name: string;
  description: string;
  version: string;
  inputSchema: ZodSchema;
  outputSchema: ZodSchema;
  timeout: number;
  retryConfig: RetryConfig;
  fallbackBehavior: FallbackBehavior;
  
  execute(params: Record<string, any>): Promise<ToolResponse>;
  validate(params: Record<string, any>): boolean;
  getSchema(): ToolSchema;
}
```

#### 5. Agent Interface

```typescript
interface Agent {
  id: string;
  name: string;
  description: string;
  systemPrompt: string;
  memoryConfig: MemoryConfig;
  providers: string[];
  tools: string[];
  hitlConfig: HITLConfig;
  
  process(input: string, context: AgentContext): Promise<AgentResponse>;
  getMemory(sessionId: string): Promise<Memory>;
  updateMemory(sessionId: string, memory: Partial<Memory>): Promise<void>;
}
```

## Data Models

### Core Data Models

#### 1. User and Authentication

```typescript
interface User {
  id: string;
  email: string;
  passwordHash: string;
  firstName?: string;
  lastName?: string;
  roles: Role[];
  tenants: Tenant[];
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}

interface Role {
  id: string;
  name: string;
  permissions: Permission[];
  description?: string;
}

interface Permission {
  id: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'execute';
}

interface Tenant {
  id: string;
  name: string;
  domain?: string;
  settings: TenantSettings;
  users: User[];
  createdAt: Date;
  updatedAt: Date;
}
```

#### 2. Agent and Tool Models

```typescript
interface Agent {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  systemPrompt: string;
  memoryConfig: {
    type: 'conversation' | 'summarization' | 'custom';
    ttl: number;
    maxTokens?: number;
    customLogic?: string;
  };
  providers: string[];
  tools: string[];
  hitlConfig: {
    enabled: boolean;
    mode: 'optional' | 'manual' | 'auto';
    timeout?: number;
    triggerConditions?: string[];
  };
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface Tool {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  version: string;
  inputSchema: Record<string, any>; // Zod schema definition
  outputSchema: Record<string, any>; // Zod schema definition
  implementation: string; // Code or reference to implementation
  timeout: number;
  retryConfig: {
    maxRetries: number;
    backoffFactor: number;
    initialDelay: number;
  };
  fallbackBehavior: {
    type: 'error' | 'default' | 'alternate';
    defaultValue?: any;
    alternateToolId?: string;
  };
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface ToolAgent {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  baseAgentId: string;
  flow: FlowDefinition;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface FlowDefinition {
  nodes: FlowNode[];
  edges: FlowEdge[];
  entryNodeId: string;
}

type FlowNode = AgentNode | ToolNode | ConditionNode | OutputNode;

interface AgentNode {
  id: string;
  type: 'agent';
  agentId: string;
  prompt?: string;
}

interface ToolNode {
  id: string;
  type: 'tool';
  toolId: string;
  parameterMappings: Record<string, ParameterMapping>;
}

interface ConditionNode {
  id: string;
  type: 'condition';
  condition: string;
}

interface OutputNode {
  id: string;
  type: 'output';
  template: string;
}

interface FlowEdge {
  id: string;
  sourceId: string;
  targetId: string;
  condition?: string;
}

type ParameterMapping = StaticMapping | ContextMapping | OutputMapping;

interface StaticMapping {
  type: 'static';
  value: any;
}

interface ContextMapping {
  type: 'context';
  path: string;
}

interface OutputMapping {
  type: 'output';
  nodeId: string;
  path: string;
}
```

#### 3. Provider Models

```typescript
interface Provider {
  id: string;
  tenantId: string;
  name: string;
  type: 'openai' | 'anthropic' | 'google' | 'mistral' | 'groq';
  apiKey: string;
  baseUrl?: string;
  organization?: string;
  models: string[];
  settings: Record<string, any>;
  enabled: boolean;
  priority: number;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 4. Session and Memory Models

```typescript
interface Session {
  id: string;
  tenantId: string;
  userId?: string;
  agentId?: string;
  toolAgentId?: string;
  widgetId?: string;
  state: Record<string, any>;
  memory: Memory;
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
}

interface Memory {
  messages: Message[];
  variables: Record<string, any>;
  summary?: string;
  metadata: Record<string, any>;
}

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}
```

#### 5. Knowledge Base Models

```typescript
interface KnowledgeBase {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  sources: KnowledgeSource[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface KnowledgeSource {
  id: string;
  knowledgeBaseId: string;
  type: 'file' | 'url' | 'text' | 'database';
  content: string;
  metadata: Record<string, any>;
  chunks: KnowledgeChunk[];
  createdAt: Date;
  updatedAt: Date;
}

interface KnowledgeChunk {
  id: string;
  sourceId: string;
  content: string;
  embedding: number[];
  metadata: Record<string, any>;
}
```

#### 6. Widget Models

```typescript
interface Widget {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  type: 'agent' | 'tool' | 'toolAgent';
  targetId: string;
  config: {
    theme: {
      mode: 'light' | 'dark' | 'auto';
      primaryColor: string;
      fontFamily?: string;
      customCSS?: string;
    };
    layout: {
      position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
      width: string;
      height: string;
      responsive: boolean;
    };
    branding: {
      logo?: string;
      name?: string;
      showPoweredBy: boolean;
    };
    behavior: {
      greeting?: string;
      placeholder?: string;
      autoOpen: boolean;
      persistSession: boolean;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}
```

#### 7. Analytics Models

```typescript
interface AnalyticsEvent {
  id: string;
  tenantId: string;
  type: string;
  timestamp: Date;
  data: Record<string, any>;
  sessionId?: string;
  userId?: string;
  agentId?: string;
  toolId?: string;
  providerId?: string;
}

interface UsageMetrics {
  id: string;
  tenantId: string;
  period: 'day' | 'week' | 'month';
  startDate: Date;
  endDate: Date;
  metrics: {
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    avgLatency: number;
    successRate: number;
    agentUsage: Record<string, number>;
    toolUsage: Record<string, number>;
    providerUsage: Record<string, number>;
  };
}
```

### Database Schema

The database schema will be implemented using TypeORM entities that map to the data models described above. The schema will include appropriate indexes, foreign keys, and constraints to ensure data integrity and query performance.

## Error Handling

### Error Types

1. **ValidationError**: Input validation failures
2. **AuthenticationError**: Authentication failures
3. **AuthorizationError**: Permission denied errors
4. **NotFoundError**: Resource not found
5. **ConflictError**: Resource conflicts
6. **ProviderError**: LLM provider errors
7. **ToolExecutionError**: Tool execution failures
8. **TimeoutError**: Operation timeouts
9. **RateLimitError**: Rate limit exceeded
10. **InternalError**: Unexpected system errors

### Error Handling Strategy

1. **Frontend Error Handling**:
   - Global error boundary for React components
   - Error state management in Zustand store
   - Toast notifications for user-facing errors
   - Form validation with error messages

2. **Backend Error Handling**:
   - NestJS exception filters for consistent error responses
   - Logging with correlation IDs
   - Graceful degradation for non-critical errors
   - Circuit breakers for external dependencies

3. **WebSocket Error Handling**:
   - Error events with detailed information
   - Automatic reconnection with exponential backoff
   - Session recovery after disconnection

## Testing Strategy

### Testing Levels

1. **Unit Testing**:
   - Test individual components and functions
   - Mock external dependencies
   - Focus on business logic and edge cases

2. **Integration Testing**:
   - Test interactions between modules
   - Use in-memory databases for data layer tests
   - Test API endpoints and WebSocket events

3. **End-to-End Testing**:
   - Test complete user flows
   - Use Cypress for frontend testing
   - Test deployment configurations

### Testing Tools

- **Backend**: Jest, Supertest
- **Frontend**: Jest, React Testing Library, Cypress
- **API**: Postman, Swagger
- **Load Testing**: k6

## Security Considerations

1. **Authentication and Authorization**:
   - JWT-based authentication with proper expiration
   - Role-based access control
   - Multi-tenant isolation

2. **Data Security**:
   - Encryption of sensitive data
   - Secure storage of API keys
   - Input validation and sanitization

3. **API Security**:
   - Rate limiting
   - CORS configuration
   - CSRF protection

4. **Infrastructure Security**:
   - HTTPS with proper TLS configuration
   - Secure WebSocket connections
   - Regular security updates

## Deployment and DevOps

### Deployment Architecture

```mermaid
graph TD
    Internet[Internet] --> NGINX[NGINX Reverse Proxy]
    NGINX --> Frontend[Next.js Frontend]
    NGINX --> Backend[NestJS Backend]
    Backend --> PostgreSQL[(PostgreSQL)]
    Backend --> Redis[(Redis)]
    
    subgraph "PM2 Process Manager"
        Frontend
        Backend
    end
    
    subgraph "SSL"
        Certbot[Certbot] --> NGINX
    end
```

### Deployment Process

1. **Build Process**:
   - Build frontend assets
   - Build backend application
   - Run tests

2. **Deployment**:
   - Deploy to target environment
   - Run database migrations
   - Update PM2 processes
   - Verify deployment

3. **Monitoring**:
   - Log aggregation
   - Performance monitoring
   - Error tracking
   - Uptime monitoring

## Implementation Plan

The implementation will follow a modular approach, with each core feature developed as a separate module. This allows for parallel development and incremental delivery of functionality.

### Phase 1: Core Infrastructure

1. Set up monorepo structure
2. Implement authentication and authorization
3. Set up database and Redis connections
4. Implement WebSocket gateway
5. Create basic frontend shell

### Phase 2: Core Modules

1. Implement Agent module
2. Implement Tool module
3. Implement Provider module
4. Implement Session module

### Phase 3: Advanced Features

1. Implement Tool Agent module
2. Implement HITL module
3. Implement Knowledge Base module
4. Implement Widget Generator module

### Phase 4: Analytics and Administration

1. Implement Analytics module
2. Implement Admin module
3. Implement Live Preview Sandbox

### Phase 5: SDK and Documentation

1. Create SDK package
2. Implement CLI tools
3. Create comprehensive documentation
4. Prepare deployment guides

## Conclusion

This design document outlines the architecture, components, data models, and implementation strategy for SynapseAI. The system is designed to be modular, scalable, and production-ready, with a focus on real-time communication, state management, and extensibility.

The implementation will follow a phased approach, with each core feature developed as a separate module. This allows for parallel development and incremental delivery of functionality.
